#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create SVC lung cancer model with correct encoding
"""

import numpy as np
import pandas as pd
import pickle
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split, RandomizedSearchCV
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Survey features as defined in the system
SURVEY_FEATURES = [
    'GENDER', 'AGE', 'SMOKING', 'YELLOW_FINGERS', 'ANXIETY', 'PEER_PRESSURE',
    'CHRONIC DISEASE', 'FATIGUE', 'ALLERGY', 'WHEEZING', 'ALCOHOL CONSUMING',
    'COUGHING', 'SHORTNESS OF BREATH', 'SWALLOWING DIFFICULTY', 'CHEST PAIN'
]

def generate_synthetic_data(n_samples=10000):
    """Generate synthetic lung cancer survey data with new encoding"""
    np.random.seed(42)
    
    data = []
    
    for _ in range(n_samples):
        # Generate features with realistic correlations
        age = np.random.randint(20, 90)
        gender = np.random.randint(0, 2)  # 0: Female, 1: Male
        
        # Smoking increases risk (1: No, 2: Yes)
        smoking = np.random.choice([1, 2], p=[0.7, 0.3])
        
        # Age and smoking influence other symptoms
        base_risk = (age - 20) / 70 + (smoking - 1) * 0.3
        
        # Generate correlated symptoms (1: No, 2: Yes)
        yellow_fingers = np.random.choice([1, 2], p=[1-min(0.8, (smoking-1)*0.6 + 0.1), min(0.8, (smoking-1)*0.6 + 0.1)])
        anxiety = np.random.choice([1, 2], p=[0.8, 0.2])
        peer_pressure = np.random.choice([1, 2], p=[0.9, 0.1])
        chronic_disease = np.random.choice([1, 2], p=[1-min(0.4, base_risk*0.3 + 0.1), min(0.4, base_risk*0.3 + 0.1)])
        fatigue = np.random.choice([1, 2], p=[1-min(0.6, base_risk*0.4 + 0.2), min(0.6, base_risk*0.4 + 0.2)])
        allergy = np.random.choice([1, 2], p=[0.8, 0.2])
        wheezing = np.random.choice([1, 2], p=[1-min(0.5, base_risk*0.3 + 0.1), min(0.5, base_risk*0.3 + 0.1)])
        alcohol = np.random.choice([1, 2], p=[0.6, 0.4])
        coughing = np.random.choice([1, 2], p=[1-min(0.7, base_risk*0.5 + 0.2), min(0.7, base_risk*0.5 + 0.2)])
        shortness_breath = np.random.choice([1, 2], p=[1-min(0.6, base_risk*0.4 + 0.1), min(0.6, base_risk*0.4 + 0.1)])
        swallowing_diff = np.random.choice([1, 2], p=[1-min(0.4, base_risk*0.2 + 0.05), min(0.4, base_risk*0.2 + 0.05)])
        chest_pain = np.random.choice([1, 2], p=[1-min(0.5, base_risk*0.3 + 0.1), min(0.5, base_risk*0.3 + 0.1)])
        
        # Calculate cancer probability based on risk factors
        cancer_prob = base_risk * 0.3
        cancer_prob += ((yellow_fingers-1) + (chronic_disease-1) + (fatigue-1) + (wheezing-1) + 
                       (coughing-1) + (shortness_breath-1) + (swallowing_diff-1) + (chest_pain-1)) * 0.08
        cancer_prob = min(0.9, max(0.05, cancer_prob))
        
        # Generate target (0: No, 1: Yes)
        lung_cancer = np.random.choice([0, 1], p=[1-cancer_prob, cancer_prob])
        
        data.append([
            gender, age, smoking, yellow_fingers, anxiety, peer_pressure,
            chronic_disease, fatigue, allergy, wheezing, alcohol,
            coughing, shortness_breath, swallowing_diff, chest_pain, lung_cancer
        ])
    
    columns = SURVEY_FEATURES + ['LUNG_CANCER']
    df = pd.DataFrame(data, columns=columns)
    return df

def main():
    """Create and save the SVC lung cancer model"""
    print("🏥 Creating SVC Lung Cancer Model")
    print("=" * 50)
    
    # Generate synthetic data
    print("📊 Generating synthetic training data...")
    df = generate_synthetic_data(10000)
    
    # Prepare features and target
    X = df[SURVEY_FEATURES].values
    y = df['LUNG_CANCER'].values
    
    print(f"✅ Generated {len(df)} samples")
    print(f"📈 Cancer cases: {y.sum()} ({y.mean()*100:.1f}%)")
    print(f"📋 Feature encoding: 1=No, 2=Yes (except Gender: 0=F, 1=M)")
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # Scale features for SVM
    print("🔧 Scaling features...")
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Create and train SVC model with RandomizedSearchCV
    print("🤖 Training SVC model with hyperparameter tuning...")
    param_grid = {
        'C': [0.001, 0.01, 0.1, 1, 10, 100], 
        'gamma': [0.001, 0.01, 0.1, 1, 10, 100]
    }
    
    svc = SVC(probability=True, random_state=42)  # Enable probability estimates
    rcv = RandomizedSearchCV(svc, param_grid, cv=5, n_iter=20, random_state=42, n_jobs=-1)
    rcv.fit(X_train_scaled, y_train)
    
    print(f"✅ Best Parameters: {rcv.best_params_}")
    
    # Evaluate model
    print("📊 Evaluating model performance...")
    y_pred = rcv.predict(X_test_scaled)
    y_prob = rcv.predict_proba(X_test_scaled)
    
    accuracy = accuracy_score(y_test, y_pred)
    print(f"✅ Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    # Print classification report
    print("\n📋 Classification Report:")
    print(classification_report(y_test, y_pred))
    
    # Create a wrapper class to include scaler
    class SVCLungCancerModel:
        def __init__(self, model, scaler):
            self.model = model
            self.scaler = scaler
        
        def predict(self, X):
            X_scaled = self.scaler.transform(X)
            return self.model.predict(X_scaled)
        
        def predict_proba(self, X):
            X_scaled = self.scaler.transform(X)
            return self.model.predict_proba(X_scaled)
        
        def decision_function(self, X):
            X_scaled = self.scaler.transform(X)
            return self.model.decision_function(X_scaled)
    
    # Create wrapper model
    wrapped_model = SVCLungCancerModel(rcv, scaler)
    
    # Save model
    model_path = "model/svc_lung_cancer.pkl"
    print(f"💾 Saving SVC model to {model_path}...")
    
    with open(model_path, 'wb') as f:
        pickle.dump(wrapped_model, f)
    
    print("✅ Model saved successfully!")
    
    # Test loading
    print("🔍 Testing model loading...")
    with open(model_path, 'rb') as f:
        loaded_model = pickle.load(f)
    
    # Test prediction with new encoding
    # Gender=M(1), Age=45, some symptoms=2 (YES)
    test_sample = np.array([[1, 45, 2, 1, 1, 1, 1, 2, 1, 2, 1, 2, 2, 1, 2]])
    pred = loaded_model.predict(test_sample)
    prob = loaded_model.predict_proba(test_sample)
    
    print(f"✅ Model loaded and tested successfully!")
    print(f"📋 Test prediction: {pred[0]} (Cancer prob: {prob[0][1]:.3f})")
    
    print("\n🎉 SVC Model creation completed!")
    print("The model uses the correct encoding:")
    print("- Gender: M=1, F=0")
    print("- All symptoms: YES=2, NO=1")
    print("- Target: YES=1, NO=0")

if __name__ == "__main__":
    main()
